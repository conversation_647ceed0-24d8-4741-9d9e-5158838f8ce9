import { CONSTANTS } from '@/constants.js';

// 基础表单项配置
const select = {
    type: 'select',
    elSelectAttrs: {
        clearable: true
    }
};

const input = {
    type: 'input'
};

const daterange = {
    type: 'daterange',
    elDatePickerAttrs: {
        'type': 'daterange',
        'range-separator': '至',
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        'value-format': 'yyyy-MM-dd'
    }
};

// 会议类型
const meetingType = {
    ...select,
    name: '会议类型',
    modelKey: 'meetingType',
    elSelectAttrs: {
        ...select.elSelectAttrs,
        placeholder: '会议类型'
    },
    elOptions: CONSTANTS.MEETING_TYPE.map((item) => ({
        label: item,
        value: item
    }))
};

// 会议状态
const meetingStatus = {
    ...select,
    name: '会议状态',
    modelKey: 'meetingStatus',
    elSelectAttrs: {
        ...select.elSelectAttrs,
        placeholder: '会议状态'
    },
    elOptions: CONSTANTS.MEETING_STATUS.map((item) => ({
        label: item,
        value: item
    }))
};

// 会议纪要状态
const meetingMinutesStatus = {
    ...select,
    name: '会议纪要状态',
    modelKey: 'meetingMinutesStatus',
    elSelectAttrs: {
        ...select.elSelectAttrs,
        placeholder: '会议纪要状态'
    },
    elOptions: CONSTANTS.MEETING_MINUTES_STATUS.map((item) => ({
        label: item,
        value: item
    }))
};

// 会议时间
const meetingTime = {
    ...daterange,
    name: '会议时间',
    modelKey: 'daterange'
};

// 会议名称
const meetingTitle = {
    ...input,
    name: '会议名称',
    modelKey: 'meetingTitle',
    elInputAttrs: {
        placeholder: '会议名称关键字'
    }
};

// 查询参数初始化
export const queryParams = {
    meetingType: '',
    meetingStatus: '',
    meetingMinutesStatus: '',
    daterange: [],
    projectId: '',
    meetingTitle: '',
    meetingMinutesFlag: false,
    type: ''
};

// 关联项目（需要动态配置）
export const createProjectSelect = (searchOptions, remoteMethod) => ({
    ...select,
    name: '关联项目',
    modelKey: 'projectId',
    elSelectAttrs: {
        'placeholder': '关联项目',
        'clearable': true,
        'remote': true,
        'filterable': true,
        'remote-method': remoteMethod
    },
    elOptions: searchOptions.map((item) => ({
        label: item.projectName,
        value: item.projectId
    }))
});

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [
        meetingType,
        meetingStatus,
        meetingMinutesStatus,
        meetingTime,
        meetingTitle
    ]
};

// 导航栏配置项
export const navItems = [
    { field: '', name: '所有', queryField: '' },
    { field: 1, name: '我组织的', queryField: 'type' },
    { field: 2, name: '我参加的', queryField: 'type' },
    { field: 3, name: '我提出过要求的', queryField: 'type' },
    { field: 4, name: '会议任务责任人是我的', queryField: 'type' },
    { field: true, name: '有会议纪要', queryField: 'meetingMinutesFlag' }
];
